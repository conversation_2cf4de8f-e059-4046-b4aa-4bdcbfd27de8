2025-08-26 18:27:45.504 [async-task-pool42] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时166ms，共1页，共2条数据，处理结果: true
2025-08-26 18:27:45.509 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:27:45.511 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时173ms，共1页，共2条数据，处理结果: true
2025-08-26 18:27:45.570 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 233毫秒
2025-08-26 18:27:47.358 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:27:47.359 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:27:47.420 [async-task-pool46] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:27:47.425 [async-task-pool45] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:27:47.437 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 79毫秒
2025-08-26 18:27:50.317 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 11秒968毫秒
2025-08-26 18:27:50.519 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 3
2025-08-26 18:27:50.543 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:27:50.562 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:27:50.563 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:27:50.569 [pool-6-thread-1] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 18:27:50.653 [pool-6-thread-1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 8047, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:40"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 365, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 18:27:26"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 191947, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 18:27:17"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 16276, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:07"}]

2025-08-26 18:27:50.657 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:4...
2025-08-26 18:27:50.658 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:27:51.530 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$init$0,81] - 最大连接：200 当前连接: 4
2025-08-26 18:27:51.583 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:27:51.584 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:27:51.589 [async-task-pool34] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 18:27:51.589 [async-task-pool57] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 18:27:52.713 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:27:52.713 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 18:27:52.714 [async-task-pool55] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 18:27:52.714 [async-task-pool56] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 18:27:52.762 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒179毫秒
2025-08-26 18:27:52.780 [pool-5-thread-3] INFO  c.r.s.t.TblDeductionDetailTask - [syncThreaten,202] - 处理威胁告警扣分结束
2025-08-26 18:27:52.796 [pool-5-thread-4] INFO  c.r.s.t.TblDeductionDetailTask - [syncThreaten,202] - 处理威胁告警扣分结束
2025-08-26 18:27:52.797 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 84毫秒
2025-08-26 18:27:55.577 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:27:55.577 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:27:55.577 [async-task-pool58] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 18:27:55.577 [async-task-pool50] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 18:27:55.578 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:25:56, endTime=2025-08-26 18:25:55, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 18:27:55.578 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:25:56, endTime=2025-08-26 18:25:55, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 18:27:55.580 [async-task-pool58] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A25%3A56&end_date=2025-08-26%2018%3A25%3A55&page=1&page_size=30
2025-08-26 18:27:55.581 [async-task-pool50] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A25%3A56&end_date=2025-08-26%2018%3A25%3A55&page=1&page_size=30
2025-08-26 18:27:55.654 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:27:55.654 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:27:55.655 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:27:55.658 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:27:55.659 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:27:55.660 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:27:55.671 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:27:55.675 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时97ms，共1页，共2条数据，处理结果: true
2025-08-26 18:27:55.682 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:27:55.683 [async-task-pool50] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时105ms，共1页，共2条数据，处理结果: true
2025-08-26 18:27:55.700 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 123毫秒
2025-08-26 18:27:57.452 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:27:57.453 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:27:57.515 [async-task-pool60] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:27:57.524 [async-task-pool54] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:27:57.535 [taskScheduler-16] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 83毫秒
2025-08-26 18:27:59.624 [taskScheduler-23] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 18:27:59.700 [taskScheduler-23] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 18:28:00.286 [taskScheduler-41] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: cfcd1406-bfce-47b5-bad2-567a7eac152a, 加密状态: 已加密, 消息类型: PING
2025-08-26 18:28:00.342 [taskScheduler-41] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 18:28:00.408 [rabbitConnectionFactory4] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$rabbitTemplate$0,290] - 消息已成功发送到交换机, messageId: cfcd1406-bfce-47b5-bad2-567a7eac152a
2025-08-26 18:28:01.586 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$init$0,81] - 最大连接：200 当前连接: 2
2025-08-26 18:28:03.022 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:03.038 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:03.038 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:03.043 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:03.044 [async-task-pool52] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 18:28:03.044 [async-task-pool61] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 18:28:03.051 [async-task-pool62] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 18:28:03.051 [async-task-pool59] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 18:28:03.442 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 404毫秒
2025-08-26 18:28:03.523 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 501毫秒
2025-08-26 18:28:03.761 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒104毫秒
2025-08-26 18:28:03.920 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 4
2025-08-26 18:28:03.966 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:03.969 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 18:28:03.970 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:03.972 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:03.975 [pool-6-thread-2] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 18:28:04.055 [pool-6-thread-2] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 191995, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 18:27:43"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 8047, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:40"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 365, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 18:27:26"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 16279, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:24"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 352, "start_time": "2025-08-25 08:03:15", "update_time": "2025-08-26 18:27:20"}]

2025-08-26 18:28:04.058 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:5...
2025-08-26 18:28:04.059 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:28:05.719 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:05.720 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:28:05.720 [async-task-pool72] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 18:28:05.720 [async-task-pool69] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 18:28:05.721 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:06, endTime=2025-08-26 18:26:05, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 18:28:05.721 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:06, endTime=2025-08-26 18:26:05, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 18:28:05.722 [async-task-pool72] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A06&end_date=2025-08-26%2018%3A26%3A05&page=1&page_size=30
2025-08-26 18:28:05.722 [async-task-pool69] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A06&end_date=2025-08-26%2018%3A26%3A05&page=1&page_size=30
2025-08-26 18:28:05.797 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:05.798 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:05.800 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:05.802 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:05.802 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:05.803 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:05.816 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:05.818 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时97ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:05.820 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:05.821 [async-task-pool72] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时100ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:05.827 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 108毫秒
2025-08-26 18:28:07.544 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:07.545 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:07.612 [async-task-pool67] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:07.617 [async-task-pool71] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:07.691 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 147毫秒
2025-08-26 18:28:11.672 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$init$0,81] - 最大连接：200 当前连接: 2
2025-08-26 18:28:13.502 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:13.502 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:13.502 [async-task-pool73] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 18:28:13.502 [async-task-pool74] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 18:28:13.540 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:13.540 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:28:13.541 [async-task-pool75] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 18:28:13.541 [async-task-pool63] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 18:28:13.594 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 93毫秒
2025-08-26 18:28:13.739 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 199毫秒
2025-08-26 18:28:17.579 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒521毫秒
2025-08-26 18:28:17.667 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:17.687 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:28:17.700 [async-task-pool77] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 18:28:17.700 [async-task-pool76] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 18:28:17.717 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:16, endTime=2025-08-26 18:26:17, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 18:28:17.722 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:16, endTime=2025-08-26 18:26:17, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 18:28:17.735 [async-task-pool77] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A16&end_date=2025-08-26%2018%3A26%3A17&page=1&page_size=30
2025-08-26 18:28:17.735 [async-task-pool76] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A16&end_date=2025-08-26%2018%3A26%3A17&page=1&page_size=30
2025-08-26 18:28:17.985 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:18.138 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 18:28:18.488 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:18.488 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:18.588 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:18.662 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:18.678 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:18.905 [async-task-pool79] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:18.787 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:18.835 [async-task-pool78] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:19.112 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒127毫秒
2025-08-26 18:28:19.122 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:19.131 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:19.132 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时1410ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:19.133 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时1416ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:19.169 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒502毫秒
2025-08-26 18:28:19.991 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 5
2025-08-26 18:28:20.051 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:20.068 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:20.071 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 18:28:20.073 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:20.074 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 18:28:20.085 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 18:28:20.132 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 191995, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 18:27:43"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 8047, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:40"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 365, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 18:27:26"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 16279, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:27:24"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 352, "start_time": "2025-08-25 08:03:15", "update_time": "2025-08-26 18:27:20"}]

2025-08-26 18:28:20.136 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:5...
2025-08-26 18:28:20.137 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:28:21.675 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$init$0,81] - 最大连接：200 当前连接: 4
2025-08-26 18:28:23.711 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:23.711 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:23.712 [async-task-pool88] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 18:28:23.712 [async-task-pool85] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 18:28:23.843 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:23.849 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:28:23.852 [async-task-pool89] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 18:28:23.852 [async-task-pool82] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 18:28:24.546 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 835毫秒
2025-08-26 18:28:24.642 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 799毫秒
2025-08-26 18:28:29.208 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:29.209 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 18:28:29.247 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:29.255 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:28:29.256 [async-task-pool90] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 18:28:29.256 [async-task-pool93] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$null$0,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 18:28:29.256 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:26, endTime=2025-08-26 18:26:29, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 18:28:29.257 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,347] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 18:26:26, endTime=2025-08-26 18:26:29, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 18:28:29.259 [async-task-pool90] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A26&end_date=2025-08-26%2018%3A26%3A29&page=1&page_size=30
2025-08-26 18:28:29.259 [async-task-pool93] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2018%3A26%3A26&end_date=2025-08-26%2018%3A26%3A29&page=1&page_size=30
2025-08-26 18:28:29.343 [async-task-pool92] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:29.343 [async-task-pool83] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 18:28:29.391 [taskScheduler-25] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 183毫秒
2025-08-26 18:28:29.394 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:29.395 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:29.395 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:29.427 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,453] - 流量风险资产总数: 2
2025-08-26 18:28:29.427 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,476] - 第1页获取2条数据
2025-08-26 18:28:29.427 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,483] - 当前页数据量(2)少于页大小(30)，已获取完所有数据
2025-08-26 18:28:29.433 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:29.433 [async-task-pool90] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时177ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:29.449 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,657] - 批量更新2条流量风险资产数据
2025-08-26 18:28:29.450 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,546] - 拉取流量风险资产数据完成，耗时193ms，共1页，共2条数据，处理结果: true
2025-08-26 18:28:29.472 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 225毫秒
2025-08-26 18:28:31.703 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$init$0,81] - 最大连接：200 当前连接: 2
2025-08-26 18:28:32.927 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 12秒791毫秒
2025-08-26 18:28:33.419 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 5
2025-08-26 18:28:33.483 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:33.486 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:33.493 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 18:28:33.494 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 18:28:33.495 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 18:28:33.498 [pool-6-thread-3] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 18:28:33.565 [pool-6-thread-3] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 424, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 18:17:51"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 301, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 18:17:51"}]

2025-08-26 18:28:33.579 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:33.579 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:28:33.760 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 181毫秒
2025-08-26 18:28:34.142 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 2
2025-08-26 18:28:34.159 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 18:28:34.163 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 18:28:34.169 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 18:28:34.235 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 16285, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:28:16"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 8051, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 18:28:10"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 192031, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 18:28:09"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 366, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 18:28:08"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 365, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 18:27:26"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 352, "start_time": "2025-08-25 08:03:15", "update_time": "2025-08-26 18:27:20"}]

2025-08-26 18:28:34.257 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:6...
2025-08-26 18:28:34.261 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 18:28:34.599 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:34.600 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 18:28:34.600 [async-task-pool104] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 18:28:34.600 [async-task-pool103] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 18:28:34.661 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 18:28:34.662 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 18:28:34.662 [async-task-pool108] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 18:28:34.662 [async-task-pool99] INFO  c.r.f.a.e.PullDataEvent - [lambda$null$0,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 18:28:34.803 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 204毫秒
2025-08-26 18:28:34.938 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 277毫秒
2025-08-26 18:28:35.229 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-26 18:28:36.053 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-26 18:28:36.080 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-26 18:28:36.082 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-26 18:28:36.083 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-08-26 18:28:36.128 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2051] - {dataSource-1} closing ...
2025-08-26 18:28:36.150 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2124] - {dataSource-1} closed
