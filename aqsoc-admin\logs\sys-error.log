2025-08-26 18:25:28.075 [async-task-pool83] ERROR c.a.d.f.s.<PERSON>atFilter - [internalAfterStatementExecute,504] - slow sql 2956 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:07"]
2025-08-26 18:25:29.444 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2498 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:25:11"]
2025-08-26 18:25:39.931 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10474 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:25:11"]
2025-08-26 18:25:42.383 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1867 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:10"]
2025-08-26 18:25:42.614 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2393 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:25:11"]
2025-08-26 18:25:43.518 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:07"]
2025-08-26 18:25:44.417 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1799 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:25:11"]
2025-08-26 18:25:52.832 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8411 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:25:11"]
2025-08-26 18:25:55.699 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2349 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:25:55.951 [async-task-pool117] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2254 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:40"]
2025-08-26 18:25:57.324 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3398 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:42"]
2025-08-26 18:25:57.813 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2109 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:26:06.625 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8798 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:26:09.084 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2176 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:26:09.111 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1809 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:40"]
2025-08-26 18:26:10.041 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2419 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:25:42"]
2025-08-26 18:26:11.038 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1949 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:26:19.647 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8601 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:25:48"]
2025-08-26 18:26:22.308 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1983 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:10"]
2025-08-26 18:26:22.445 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2430 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:23.756 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3098 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:12"]
2025-08-26 18:26:24.926 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2473 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:32.482 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7554 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:34.908 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1796 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:10"]
2025-08-26 18:26:35.271 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2470 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:36.003 [async-task-pool162] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2627 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:12"]
2025-08-26 18:26:37.093 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1816 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:44.453 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7356 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:47.058 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1897 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:10"]
2025-08-26 18:26:47.460 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2618 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:48.179 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2737 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:12"]
2025-08-26 18:26:49.472 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2008 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:57.157 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7681 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:26:16"]
2025-08-26 18:26:59.694 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1962 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:40"]
2025-08-26 18:27:00.160 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2757 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:01.782 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3810 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:46"]
2025-08-26 18:27:02.365 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2201 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:09.812 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7443 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:13.089 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2259 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:40"]
2025-08-26 18:27:13.258 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2793 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:14.871 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3753 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:26:46"]
2025-08-26 18:27:15.877 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2606 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:25.726 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9696 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:26:49"]
2025-08-26 18:27:28.570 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1825 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:10"]
2025-08-26 18:27:28.640 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2159 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:29.704 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2600 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:07"]
2025-08-26 18:27:30.538 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1878 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:38.100 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7557 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:40.579 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1908 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:10"]
2025-08-26 18:27:40.840 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2486 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:41.676 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2770 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:07"]
2025-08-26 18:27:42.865 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2022 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:50.314 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7445 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:53.208 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2226 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:40"]
2025-08-26 18:27:53.583 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2921 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:27:54.328 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3137 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:07"]
2025-08-26 18:27:55.655 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2068 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:28:03.754 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8093 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:27:17"]
2025-08-26 18:28:05.849 [async-task-pool53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1523 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:40"]
2025-08-26 18:28:06.038 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1968 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
2025-08-26 18:28:07.501 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2964 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:24"]
2025-08-26 18:28:08.572 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2526 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
2025-08-26 18:28:16.988 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8229 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
2025-08-26 18:28:22.300 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1842 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:40"]
2025-08-26 18:28:22.553 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2403 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
2025-08-26 18:28:23.594 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2904 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 18:27:24"]
2025-08-26 18:28:24.461 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1897 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
2025-08-26 18:28:32.918 [async-task-pool80] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8427 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 18:27:43"]
